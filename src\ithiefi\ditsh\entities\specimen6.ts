import { Entity, GameMode, Player, Vector3, system, world } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

const TELEPORT_OFFSET_DISTANCE = 3;
const LOOK_DETECTION_RADIUS = 128;
const TELEPORT_COOLDOWN_TICKS = 3 * 20; // 3 seconds in ticks

// Raycast parameters for line-of-sight detection
const RAYCAST_LENGTH = 128; // Maximum raycast distance
const RAYCAST_STEP = 2; // Step size between raycast points
const RAYCAST_RADIUS = 5; // Radius around each raycast point to check for Specimen 6

// Track player positions for idle detection
const playerPositionTracker = new Map<string, Vector3>();

// Track specimen 6 teleport cooldowns
const specimen6CooldownTracker = new Map<string, number>();

// Track specimen 6 entities for idle player checking
const activeSpecimen6Entities = new Set<string>();

/**
 * Checks if a player is looking at Specimen 6 using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 *
 * @param player - The player to check
 * @param specimen6Location - The location of Specimen 6
 * @returns True if the player is looking at Specimen 6
 */
function isPlayerLookingAtSpecimen6(player: Player, specimen6Location: Vector3): boolean {
  try {
    const playerLocation: Vector3 = player.location;
    const viewDirection: Vector3 = player.getViewDirection();

    // Check if Specimen 6 is within the player's field of view using raycast
    const raycastResults = fixedLenRaycast(
      playerLocation,
      viewDirection,
      RAYCAST_LENGTH,
      RAYCAST_STEP
    );

    // Check each raycast point for proximity to Specimen 6
    for (const raycastPoint of raycastResults) {
      const distanceToSpecimen6: number = getDistance(raycastPoint, specimen6Location);
      if (distanceToSpecimen6 <= RAYCAST_RADIUS) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.warn(`Failed to check if player is looking at Specimen 6: ${error}`);
    return false;
  }
}

// Idle player detection is now handled by the scheduler component in the entity

/**
 * Checks for idle players around a specific specimen 6 entity.
 * This function is called by the scheduler component event trigger.
 *
 * @param specimen6 - The specimen 6 entity that triggered the check
 */
export function specimen6CheckIdlePlayers(specimen6: Entity): void {
  try {
    const specimen6Location: Vector3 = specimen6.location;

    // Get all players within a large radius
    const players: Player[] = specimen6.dimension.getPlayers({
      location: specimen6Location,
      maxDistance: 256,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    let nearestIdlePlayer: Player | undefined;
    let nearestDistance: number = Infinity;

    // Check each player's position against their stored position
    for (const player of players) {
      const playerId: string = player.id;
      const currentPosition: Vector3 = player.location;
      const storedPosition = playerPositionTracker.get(playerId);

      if (storedPosition) {
        // Check if player has been idle (same position for 8 seconds)
        const distanceMoved: number = getDistance(storedPosition, currentPosition);

        if (distanceMoved <= 0.5) {
          // Player is idle, check if they're the nearest
          const distanceToSpecimen6: number = getDistance(specimen6Location, currentPosition);
          if (distanceToSpecimen6 < nearestDistance) {
            nearestDistance = distanceToSpecimen6;
            nearestIdlePlayer = player;
          }
        }
      }

      // Update stored position for next check
      playerPositionTracker.set(playerId, currentPosition);
    }

    // If we found an idle player, teleport to them
    if (nearestIdlePlayer) {
      // Teleport specimen 6 directly to the idle player's location
      specimen6.teleport(nearestIdlePlayer.location);

      // Play step sound effect
      specimen6.dimension.playSound("mob.ditsh.specimen6.step", nearestIdlePlayer.location);

      // Trigger movement component group
      specimen6.triggerEvent("ditsh:teleport_to_idle_player");
    }
  } catch (error) {
    console.warn(`Failed to check for idle players: ${error}`);
  }
}

/**
 * Finds players who are not looking at Specimen 6 and are within range.
 *
 * @param specimen6 - The Specimen 6 entity
 * @returns Array of players not looking at Specimen 6
 */
function getPlayersNotLookingAtSpecimen6(specimen6: Entity): Player[] {
  const specimen6Location: Vector3 = specimen6.location;
  const playersNotLooking: Player[] = [];

  // Get all valid players within detection radius
  const players: Player[] = specimen6.dimension.getPlayers({
    location: specimen6Location,
    maxDistance: LOOK_DETECTION_RADIUS,
    excludeGameModes: [GameMode.Creative, GameMode.Spectator]
  });

  for (const player of players) {
    if (!isPlayerLookingAtSpecimen6(player, specimen6Location)) {
      playersNotLooking.push(player);
    }
  }

  return playersNotLooking;
}

// Idle player detection is now handled by the interval system in checkForIdlePlayers()

/**
 * Teleports Specimen 6 behind a player who is not looking at him.
 * This function is called by the environment sensor trigger.
 *
 * @param specimen6 - The Specimen 6 entity
 */
export function specimen6TeleportBehindPlayer(specimen6: Entity): void {
  try {
    const specimen6Id: string = specimen6.id;
    const currentTick: number = system.currentTick;

    // Check cooldown
    const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
    if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
      return;
    }

    const playersNotLooking: Player[] = getPlayersNotLookingAtSpecimen6(specimen6);
    
    if (playersNotLooking.length === 0) {
      return;
    }

    // Select a random player not looking at Specimen 6
    const targetPlayer: Player = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)]!;
    const playerViewDirection: Vector3 = targetPlayer.getViewDirection();

    // Try multiple teleportation strategies
    let finalTeleportLocation: Vector3 | undefined;

    // Strategy 1: Teleport behind the player (opposite to view direction)
    const behindPlayerLocation: Vector3 = {
      x: targetPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
      y: targetPlayer.location.y,
      z: targetPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
    };

    const behindPlayerBlock = specimen6.dimension.getBlock(behindPlayerLocation);
    if (behindPlayerBlock?.isAir) {
      finalTeleportLocation = behindPlayerLocation;
    }

    // Strategy 2: Try random locations around the player if behind fails
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        targetPlayer.location,
        specimen6.dimension,
        2, // baseOffset: 2 blocks minimum distance
        4, // additionalOffset: up to 6 blocks total distance (2+4)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 3: Final fallback - teleport near player
    if (!finalTeleportLocation) {
      finalTeleportLocation = {
        x: targetPlayer.location.x + (Math.random() - 0.5) * 4,
        y: targetPlayer.location.y,
        z: targetPlayer.location.z + (Math.random() - 0.5) * 4
      };
    }

    // Teleport Specimen 6
    specimen6.teleport(finalTeleportLocation);

    // Play step sound effect
    specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);

    // Update cooldown
    specimen6CooldownTracker.set(specimen6Id, currentTick);

  } catch (error) {
    console.warn(`Failed to teleport Specimen 6 behind player: ${error}`);
  }
}

/**
 * Teleports Specimen 6 to an idle player (idle for more than 8 seconds).
 * This function is called by the environment sensor trigger.
 * Note: The actual idle detection is now handled by the interval system.
 *
 * @param specimen6 - The Specimen 6 entity
 */
export function specimen6TeleportToIdlePlayer(specimen6: Entity): void {
  // This function is now mainly used to trigger the movement component group
  // The actual idle player teleportation is handled by the interval system in checkForIdlePlayers()
  try {
    // Just ensure the entity is registered for idle checking
    activeSpecimen6Entities.add(specimen6.id);
  } catch (error) {
    console.warn(`Failed to register Specimen 6 for idle player detection: ${error}`);
  }
}

/**
 * Cleanup function to remove expired player tracking entries.
 * Should be called periodically to prevent memory leaks.
 */
export function cleanupSpecimen6PlayerTracking(): void {
  const currentTick: number = system.currentTick;

  // Cleanup player position tracker (remove entries older than 10 minutes)
  const expiredPositions: string[] = [];
  for (const playerId of playerPositionTracker.keys()) {
    // We'll remove entries that haven't been updated in a while
    // This is a simple cleanup - in a real scenario you'd track last update time
    try {
      // Try to get the player to see if they still exist
      const player = world.getAllPlayers().find(p => p.id === playerId);
      if (!player) {
        expiredPositions.push(playerId);
      }
    } catch (error) {
      expiredPositions.push(playerId);
    }
  }

  for (const playerId of expiredPositions) {
    playerPositionTracker.delete(playerId);
  }

  // Cleanup teleport cooldown tracker
  const expiredCooldowns: string[] = [];
  for (const [specimen6Id, lastTeleportTick] of specimen6CooldownTracker.entries()) {
    if (currentTick - lastTeleportTick > TELEPORT_COOLDOWN_TICKS * 2) {
      expiredCooldowns.push(specimen6Id);
    }
  }

  for (const specimen6Id of expiredCooldowns) {
    specimen6CooldownTracker.delete(specimen6Id);
  }

  // Cleanup active specimen 6 entities
  const expiredEntities: string[] = [];
  for (const specimen6Id of activeSpecimen6Entities) {
    try {
      // Check if entity still exists by trying to find it
      let entityExists = false;
      for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
        const entities = dimension.getEntities({ type: "ditsh:specimen6" });
        if (entities.some(e => e.id === specimen6Id)) {
          entityExists = true;
          break;
        }
      }
      if (!entityExists) {
        expiredEntities.push(specimen6Id);
      }
    } catch (error) {
      expiredEntities.push(specimen6Id);
    }
  }

  for (const specimen6Id of expiredEntities) {
    activeSpecimen6Entities.delete(specimen6Id);
  }
}
